﻿using AIServices;
using AIServices.Models.Chat;
using HIH.Framework.BaseUIDX;
using HIH.Framework.Common.Data;
using Newtonsoft.Json;
using Spire.Pdf;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.CMS.Main
{
    public partial class frmPDFJK : HIH.Framework.BaseUIDX.BaseCRMForm
    {


        #region 字段和属性

        // AI服务相关
        private readonly Ai ai;
        private readonly ChatRequest chatRequest;

        // PDF处理相关
        private string currentFilePath = "";
        private int currentPageIndex = 0;  // 当前处理的页码索引
        private int totalPages = 0;        // PDF总页数
        private bool isEnd = false;

        // 数据相关
        private string sjID = "";
        private List<dynamic> allDetails = new List<dynamic>(); // 存储所有明细数据
        private DataTable dt;              // 主表数据存储
        private DataTable detailDT;        // 明细表数据存储
        private DataTable initialDetailDT; // 初始明细表数据存储
        private bool isSave = false;       // true:已经保存，false:新建

        // 计时相关
        private DateTime startTime;
        private TimeSpan elapsedTime;
        private bool isTimerRunning = false;

        #endregion

        public frmPDFJK()
        {
            InitializeComponent();
            isLoadPerm = false;

            // 初始化AI服务
            ai = new Ai("sk-66716486ee3148cc8160148cfe07b41a", "cebfadc871fd4b1ea2ba7e0d85646e79");
            chatRequest = new ChatRequest() { Temperature = 0.1 };
        }

        /// <summary>
        /// 选择文件并处理第一页
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void btnSelectFile_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // 选择文件
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "PDF文件|*.pdf",
                Title = "选择PDF文件"
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                Clear();
                try
                {
                    allDetails.Clear();
                    // 保存文件路径
                    string filePath = openFileDialog.FileName;
                    currentFilePath = filePath;

                    // 获取PDF总页数
                    totalPages = GetPdfPageCount(filePath);

                    // 重置当前页码索引
                    currentPageIndex = 0;
                    isEnd = false;

                    ShowWaitForm.WaitFormShow(this, "正在提取...");

                    // 开始计时
                    StartTimer();

                    // 提取第一页内容，检查是否是进口保税核注清单
                    string firstPageContent = ExtractPdfPageContent(currentFilePath, 0).ToString();
                    if (!firstPageContent.Contains("进口保税核注清单"))
                    {
                        throw new Exception("当前识别的PDF不是 '进口保税核注清单' !");
                    }

                    // 使用批量并发请求处理
                    await ProcessPdfWithBatchRequestsAsync(firstPageContent);
                }
                catch (Exception ex)
                {
                    StopTimer();
                    ShowWaitForm.SetWaitFormStop(ex.Message);
                }
            }
        }
        //主数据请求


        private void btnClear_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //清空
            Clear();
        }

        private void Clear()
        {
            gd.DataSource = null;
            txtHZQDBH.Text = "";
            txtQDLX.Text = "";
            txtBGDBH.Text = "";
            txtZSHKSJ.Text = "";
            txtGLBGDBH.Text = "";
            txtCZR.Text = "";
            txtCZSJ.Text = "";

            // 清空数据表
            if (detailDT != null)
            {
                detailDT.Clear();
            }

            // 同时清空初始数据表
            if (initialDetailDT != null)
            {
                initialDetailDT.Clear();
            }

            isEnd = false;

            if (dt != null)
            {
                dt.Clear();
            }

            isSave = false;
            ckbEdit.Checked = false;
            // 停止计时器并重置时间显示
            StopTimer();
            txtTime.Text = "00:00:00";
        }

        private void btnReturn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DoReturn();
            // 查看richTextBox1的内容
            //var a = richTextBox1.Text;
        }

        private void btnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                bs.EndEdit();
                gdv.CloseEditor();
                gdv.UpdateCurrentRow();

                // 检查核注清单编号是否为空
                if (string.IsNullOrEmpty(txtHZQDBH.Text))
                {
                    ICF.ISD.ShowError("核注清单编号不能为空！");
                    return;
                }

                // 检查明细表是否有数据
                if (detailDT == null || detailDT.Rows.Count == 0)
                {
                    ICF.ISD.ShowError("明细数据不能为空！");
                    return;
                }

                // 检查明细表中的必填字段
                foreach (DataRow row in detailDT.Rows)
                {

                    // 检查商品名称是否为空
                    if (row["商品名称"] == DBNull.Value || string.IsNullOrEmpty(row["商品名称"].ToString()))
                    {
                        ICF.ISD.ShowError("商品名称不能为空！");
                        return;
                    }

                    // 检查申报数量是否为空
                    if (row["申报数量"] == DBNull.Value)
                    {
                        ICF.ISD.ShowError("申报数量不能为空！");
                        return;
                    }
                }

                // 检查备案序号是否重复
                Dictionary<string, int> baSerialNumbers = new Dictionary<string, int>();
                foreach (DataRow row in detailDT.Rows)
                {
                    string baSerialNumber = row["备案序号"].ToString();
                    if (string.IsNullOrEmpty(baSerialNumber))
                    {
                        continue;
                    }
                    object baxhCheck = SqlHelper.ExecuteScaler("select 备案序号 from 进口核注清单明细表" +
                        " where 备案序号 = '" + baSerialNumber + "' and ID != '" + row["ID"] + "'");
                    if (baSerialNumbers.ContainsKey(baSerialNumber))
                    {
                        ICF.ISD.ShowError($"备案序号 '{baSerialNumber}' 在明细表中重复，不允许新增！");
                        return;
                    }
                    if (baxhCheck != null)
                    {
                        ICF.ISD.ShowError($"备案序号 '{baSerialNumber}' 已经存在，不允许新增！");
                        return;
                    }
                    baSerialNumbers.Add(baSerialNumber, 1);
                }

                string id = txtID.Text;

                //将表头保存到：进口核注清单表
                string sql = "select * from 进口核注清单表 where 核注清单编号 = '" + txtHZQDBH.Text + "' and ID != '" + txtID.Text + "'";
                DataTable checkHZQD = SqlHelper.FillDataTable(sql);
                if (checkHZQD.Rows.Count > 0)
                {
                    throw new Exception("当前识别的进口核注清单已经存在！");
                }
                sql = "select * from 进口核注清单表 where ID = '" + txtID.Text + "'";
                DataTable jkhzDT = SqlHelper.FillDataTable(sql);
                DataRow dr;
                if (jkhzDT.Rows.Count > 0)
                {
                    if (!isSave)
                    {
                        throw new Exception("当前识别的进口核注清单已经存在！");
                    }
                    dr = jkhzDT.Rows[0];
                }
                else
                {
                    DateTime currentDate = DateTime.Now;
                    string strId = "JKHZ" + (currentDate.Year % 100).ToString("00") + currentDate.Month.ToString("00");
                    //获取进口核注清单表的MaxId
                    string maxID = SqlHelper.GetMaxID("ID", "进口核注清单表", "where ID like '" + strId + "%'");
                    if (maxID == "")
                    {
                        id = strId + "001";
                    }
                    else
                    {
                        string lastFourDigits = maxID.Substring(maxID.Length - 3);
                        // 将截取的字符串转换为数字
                        int lastFourNumber = int.Parse(lastFourDigits) + 1;
                        // 拼接最终结果
                        id = strId + lastFourNumber.ToString("D3");
                    }
                    txtID.Text = id;
                    dr = jkhzDT.NewRow();
                    jkhzDT.Rows.Add(dr);
                    dr["ID"] = txtID.Text;
                    foreach (DataRow row in detailDT.Rows)
                    {
                        row["上级ID"] = txtID.Text;
                    }
                }
                dr["核注清单编号"] = txtHZQDBH.Text;
                dr["清单类型"] = txtQDLX.Text;
                dr["报关单编号"] = txtBGDBH.Text;
                dr["正式核扣时间"] = txtZSHKSJ.Text;
                dr["操作人"] = txtCZR.Text;
                dr["操作时间"] = txtCZSJ.Text;
                dr["关联报关单编号"] = txtGLBGDBH.Text;
                SqlHelper.UpdateDataTable(sql, jkhzDT);
                if (detailDT.GetChanges() != null && detailDT.GetChanges().Rows.Count > 0)
                {
                    SqlHelper.UpdateDataTable("select * from 进口核注清单明细表 where 上级ID = '" + txtID.Text + "'", detailDT.GetChanges());
                }
                ICF.ISD.ShowTips("保存成功");
                LoadData();
            }
            catch (Exception exc)
            {
                if (ShowWaitForm.WaitMessageForm != null && !ShowWaitForm.WaitMessageForm.IsDisposed)
                    ShowWaitForm.CloseWaitForm();
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void LoadData()
        {
            List<string> sqlList = new List<string>();
            string sql = "select * from 进口核注清单表 where ID = '" + txtID.Text + "'";
            string sql2 = "select * from 进口核注清单明细表 where 上级ID = '" + txtID.Text + "'";
            sqlList.Add(sql);
            sqlList.Add(sql2);
            using (DataSet ds = SqlHelper.FillDataSet(sqlList))
            {
                dt = ds.Tables[0];
                detailDT = ds.Tables[1];
                // 创建initialDetailDT的深度复制，而不是引用
                initialDetailDT = ds.Tables[1].Copy();
            };
            if (dt.Rows.Count > 0)
            {
                isSave = true;
            }
            gd.DataSource = detailDT;
            gdv.BestFitColumns();
            ckbEdit.Checked = false;
        }

        private void frmPDFJK_Load(object sender, EventArgs e)
        {
            if (vInParam != null)
            {
                txtID.Text = ((ArrayList)vInParam)[0].ToString();
            }
            LoadData();
            bs.DataSource = dt;
            DataBind();

            // 初始化计时器
            timer.Interval = 1000; // 每秒更新一次
            timer.Tick += Timer_Tick;
        }

        private void gdv_CustomRowCellEdit(object sender, DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs e)
        {
            if (isSave)
            {
                DataRow currentRow = gdv.GetDataRow(e.RowHandle);

                // 确保行索引在有效范围内
                if (currentRow != null && e.RowHandle >= 0 && e.RowHandle < initialDetailDT.Rows.Count)
                {
                    DataRow initialRow = initialDetailDT.Rows[e.RowHandle];
                    bool canEdit = true;

                    // 使用初始数据来判断是否可以编辑
                    // 如果申报数量不等于剩余申报数量，说明已经被部分使用，不允许编辑
                    if (initialRow["已核销申报数量"] != DBNull.Value)
                    {
                        decimal originalAmount;
                        if (decimal.TryParse(initialRow["已核销申报数量"].ToString(), out originalAmount))
                        {
                            if (originalAmount != 0)
                                canEdit = false;
                        }
                    }

                    // 如果法定数量不等于剩余法定数量，说明已经被部分使用，不允许编辑
                    if (initialRow["已核销法定数量"] != DBNull.Value)
                    {
                        decimal originalAmount;
                        if (decimal.TryParse(initialRow["已核销法定数量"].ToString(), out originalAmount))
                        {
                            if (originalAmount != 0)
                                canEdit = false;
                        }
                    }

                    // 如果第二法定数量不等于剩余第二法定数量，说明已经被部分使用，不允许编辑
                    if (initialRow["已核销第二法定数量"] != DBNull.Value)
                    {
                        decimal originalAmount;
                        if (decimal.TryParse(initialRow["已核销第二法定数量"].ToString(), out originalAmount))
                        {
                            if (originalAmount != 0)
                                canEdit = false;
                        }
                    }

                    // 如果不能编辑，设置为只读
                    if (!canEdit)
                    {
                        e.RepositoryItem = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit
                        {
                            ReadOnly = true
                        };
                    }
                }
            }
        }

        private void gdv_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            // 检查是否是需要同步的列
            if (e.Column.FieldName == "申报数量" ||
                e.Column.FieldName == "法定数量" ||
                e.Column.FieldName == "第二法定数量")
            {
                // 获取当前行
                DataRow row = gdv.GetDataRow(e.RowHandle);
                if (row != null)
                {
                    // 同步更新对应的剩余数量
                    if (e.Column.FieldName == "申报数量" && e.Value != DBNull.Value)
                    {
                        row["剩余申报数量"] = e.Value;
                    }
                    else if (e.Column.FieldName == "法定数量" && e.Value != DBNull.Value)
                    {
                        row["剩余法定数量"] = e.Value;
                    }
                    else if (e.Column.FieldName == "第二法定数量" && e.Value != DBNull.Value)
                    {
                        row["剩余第二法定数量"] = e.Value;
                    }

                    // 刷新视图
                    gdv.RefreshData();
                }
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtID.Text))
                {
                    return;
                }

                // 检查明细数据是否已被使用
                bool canDelete = true;
                foreach (DataRow row in detailDT.Rows)
                {
                    if (string.IsNullOrEmpty(row["备案序号"].ToString()))
                    {
                        continue;
                    }
                    // 使用更安全的数值比较方式
                    if (row["已核销申报数量"] != DBNull.Value)
                    {
                        decimal originalAmount;
                        if (decimal.TryParse(row["已核销申报数量"].ToString(), out originalAmount))
                        {
                            if (originalAmount != 0)
                            {
                                canDelete = false;
                                break;
                            }
                        }
                    }

                    if (row["已核销法定数量"] != DBNull.Value)
                    {
                        decimal originalAmount;
                        if (decimal.TryParse(row["已核销法定数量"].ToString(), out originalAmount))
                        {
                            if (originalAmount != 0)
                            {
                                canDelete = false;
                                break;
                            }
                        }
                    }

                    if (row["已核销第二法定数量"] != DBNull.Value)
                    {
                        decimal originalAmount;
                        if (decimal.TryParse(row["已核销第二法定数量"].ToString(), out originalAmount))
                        {
                            if (originalAmount != 0)
                            {
                                canDelete = false;
                                break;
                            }
                        }
                    }
                }

                if (!canDelete)
                {
                    throw new Exception("当前核注清单已被使用，不能删除！");
                }

                // 执行删除操作
                string delSql = "delete from 进口核注清单表 where ID = '" + txtID.Text + "'";
                var delDetailSql = "delete from 进口核注清单明细表 where 上级ID = '" + txtID.Text + "'";
                List<string> sqlList = new List<string>() {
                    delSql,delDetailSql
                };

                SqlHelper.ExecuteNonSQlTran(sqlList);
                ICF.ISD.ShowTips("删除成功！");
                Clear();
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void btnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }


        #region PDF处理与AI识别

        /// <summary>
        /// 使用批量并发请求处理PDF，实现按顺序实时显示结果
        /// </summary>
        /// <param name="firstPageContent">第一页PDF内容</param>
        private async Task ProcessPdfWithBatchRequestsAsync(string firstPageContent)
        {
            try
            {
                // 设置最大并发请求数
                const int maxConcurrentRequests = 5; // 稍微降低并发数，提高稳定性

                // 准备所有页面的请求
                List<ChatRequest> requests = PrepareAllRequests(firstPageContent);
                int totalRequests = requests.Count;

                if (totalRequests == 0)
                {
                    ShowWaitForm.SetWaitFormStop("未能提取有效内容，请检查PDF文件");
                    return;
                }

                ShowWaitForm.SetWaitFormDesption($"开始识别...共{totalRequests}个");

                // 初始化结果处理所需的数据结构
                var processingData = InitializeProcessingData(totalRequests);

                // 创建请求队列
                var requestQueue = new ConcurrentQueue<(int index, ChatRequest request)>(
                    requests.Select((req, idx) => (idx, req)));

                // 为每个请求添加完成回调
                SetupCompletionCallbacks(processingData, totalRequests);

                // 分批处理请求
                await ProcessRequestsInBatches(requestQueue, processingData.CompletionSources,
                    maxConcurrentRequests, totalRequests);
            }
            catch (Exception ex)
            {
                StopTimer();
                ShowWaitForm.SetWaitFormStop($"批量处理PDF时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 准备所有页面的AI请求
        /// </summary>
        private List<ChatRequest> PrepareAllRequests(string firstPageContent)
        {
            List<ChatRequest> requests = new List<ChatRequest>();

            // 处理第一页主表信息
            int index = firstPageContent.IndexOf("核注清单明细");
            if (index != -1)
            {
                // 添加主表信息请求
                requests.Add(new ChatRequest
                {
                    Question = firstPageContent.Substring(0, index) + GetMainTablePrompt(),
                    Temperature = 0.1
                });

                // 添加第一页明细信息请求
                requests.Add(new ChatRequest
                {
                    Question = firstPageContent.Substring(index + "核注清单明细".Length) + GetDetailPrompt(),
                    Temperature = 0.3
                });
            }

            // 从第二页开始添加请求
            for (int i = 1; i < totalPages && !isEnd; i++)
            {
                string pageContent = ExtractPdfPageContent(currentFilePath, i).ToString();
                if (string.IsNullOrEmpty(pageContent.Trim()) || pageContent == " \r\n")
                {
                    isEnd = true;
                    break;
                }

                requests.Add(new ChatRequest
                {
                    Question = pageContent + GetDetailPrompt(),
                    Temperature = 0.3
                });
            }

            return requests;
        }

        /// <summary>
        /// 获取主表信息的提示词
        /// </summary>
        private string GetMainTablePrompt()
        {
            return @"
请仔细读取以上内容，注意我的\r、\n、\r、\n 换行空格信息。正式核扣时间填写日期时间值，不要填写别的值：
请获取其中的 核注清单编号、清单类型、报关单编号、正式核扣时间（时间类型)、关联报关单编号,
对于这些数值字段，请务必保持原始格式和精度，不要进行四舍五入或其他数值处理,
请以json的数据形式返回，格式如下：
{
    ""核注清单编号"":"""",
    ""清单类型"":"""",
    ""报关单编号"":"""",
    ""正式核扣时间"":"""",
    ""关联报关单编号"":""""
}";
        }

        /// <summary>
        /// 初始化处理数据所需的数据结构
        /// </summary>
        private (TaskCompletionSource<string>[] CompletionSources, string[] Results,
            bool[] ResultReady, object LockObj, int NextIndexToProcess)
            InitializeProcessingData(int totalRequests)
        {
            // 创建任务完成源数组
            var completionSources = Enumerable.Range(0, totalRequests)
                .Select(_ => new TaskCompletionSource<string>())
                .ToArray();

            // 创建结果存储数组
            string[] results = new string[totalRequests];
            bool[] resultReady = new bool[totalRequests];

            // 创建锁对象和处理索引
            object lockObj = new object();
            int nextIndexToProcess = 0;

            return (completionSources, results, resultReady, lockObj, nextIndexToProcess);
        }

        /// <summary>
        /// 设置请求完成回调
        /// </summary>
        private void SetupCompletionCallbacks(
            (TaskCompletionSource<string>[] CompletionSources, string[] Results,
            bool[] ResultReady, object LockObj, int NextIndexToProcess) data,
            int totalRequests)
        {
            // 创建处理结果的委托
            Action processNextResults = () =>
            {
                lock (data.LockObj)
                {
                    // 处理所有已准备好且按顺序的结果
                    while (data.NextIndexToProcess < totalRequests &&
                           data.ResultReady[data.NextIndexToProcess])
                    {
                        int currentIndex = data.NextIndexToProcess;
                        string content = data.Results[currentIndex];

                        this.Invoke((MethodInvoker)delegate
                        {
                            try
                            {
                                // 更新进度
                                data.NextIndexToProcess++;
                                // 处理主表信息（索引0）或明细信息
                                if (currentIndex == 0)
                                {
                                    ProcessAndBindJsonData(content, true);
                                }
                                else
                                {
                                    ProcessAndBindDetailData(content);
                                }
                                ShowWaitForm.SetWaitFormDesption($"已处理 {data.NextIndexToProcess}/{totalRequests}...");


                            }
                            catch (Exception ex)
                            {
                                ShowWaitForm.SetWaitFormShowError($"处理页面 {currentIndex} 时出错: {ex.Message}");
                            }
                            // 检查是否所有页面都处理完成
                            if (data.NextIndexToProcess >= totalRequests)
                            {
                                StopTimer();
                                ShowWaitForm.SetWaitFormStop("识别成功，请检查数据！");
                            }
                        });
                    }
                }
            };

            // 为每个请求添加完成回调
            for (int i = 0; i < totalRequests; i++)
            {
                int requestIndex = i; // 捕获循环变量
                _ = data.CompletionSources[i].Task.ContinueWith(t =>
                {
                    try
                    {
                        if (t.Status == TaskStatus.RanToCompletion)
                        {
                            // 存储结果，但不立即处理
                            lock (data.LockObj)
                            {
                                data.Results[requestIndex] = t.Result;
                                data.ResultReady[requestIndex] = true;
                                processNextResults();
                            }
                        }
                        else if (t.IsFaulted)
                        {
                            HandleTaskFault(t, requestIndex, data, processNextResults);
                        }
                    }
                    catch (Exception ex)
                    {
                        HandleCallbackException(ex, requestIndex, data, processNextResults);
                    }
                }, TaskScheduler.Default);
            }
        }

        /// <summary>
        /// 处理任务失败情况
        /// </summary>
        private void HandleTaskFault(Task<string> task, int requestIndex,
            (TaskCompletionSource<string>[] CompletionSources, string[] Results,
            bool[] ResultReady, object LockObj, int NextIndexToProcess) data,
            Action processNextResults)
        {
            Exception ex = task.Exception?.InnerException ?? task.Exception;
            string errorMessage = ex?.Message ?? "未知错误";

            this.Invoke(new Action(() =>
            {
                ShowWaitForm.SetWaitFormShowError($"PDF {requestIndex + 1} 失败: {errorMessage}");

                lock (data.LockObj)
                {
                    data.Results[requestIndex] = $"{{\"error\": \"{errorMessage}\"}}";
                    data.ResultReady[requestIndex] = true;
                    processNextResults();
                }
            }));
        }

        /// <summary>
        /// 处理回调中的异常
        /// </summary>
        private void HandleCallbackException(Exception ex, int requestIndex,
            (TaskCompletionSource<string>[] CompletionSources, string[] Results,
            bool[] ResultReady, object LockObj, int NextIndexToProcess) data,
            Action processNextResults)
        {
            this.Invoke(new Action(() =>
            {
                ShowWaitForm.SetWaitFormShowError($"处理PDF {requestIndex + 1} 结果时出错: {ex.Message}");

                lock (data.LockObj)
                {
                    data.Results[requestIndex] = $"{{\"error\": \"{ex.Message}\"}}";
                    data.ResultReady[requestIndex] = true;
                    processNextResults();
                }
            }));
        }

        /// <summary>
        /// 分批处理请求
        /// </summary>
        private async Task ProcessRequestsInBatches(
            ConcurrentQueue<(int index, ChatRequest request)> requestQueue,
            TaskCompletionSource<string>[] completionSources,
            int maxConcurrentRequests, int totalRequests)
        {
            int batchSize = Math.Min(maxConcurrentRequests, totalRequests);
            int totalBatches = (int)Math.Ceiling((double)totalRequests / batchSize);

            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
            {
                // 启动当前批次的工作线程
                int currentBatchSize = Math.Min(batchSize, totalRequests - batchIndex * batchSize);

                await Task.WhenAll(Enumerable.Range(0, currentBatchSize)
                    .Select(_ => ProcessRequestsAsync(requestQueue, completionSources)));

                // 批次之间添加短暂延迟，避免服务端限流
                if (batchIndex < totalBatches - 1)
                {
                    await Task.Delay(500);
                }
            }
        }



        /// <summary>
        /// 处理请求队列中的任务
        /// </summary>
        /// <param name="requestQueue">请求队列</param>
        /// <param name="completionSources">任务完成源数组</param>
        /// <returns>处理任务</returns>
        private async Task ProcessRequestsAsync(
            ConcurrentQueue<(int index, ChatRequest request)> requestQueue,
            TaskCompletionSource<string>[] completionSources)
        {
            while (requestQueue.TryDequeue(out var item))
            {
                try
                {
                    // 添加小延迟，避免请求过于密集导致服务端限流
                    await Task.Delay(100);

                    // 记录开始时间
                    //var requestStartTime = DateTime.Now;

                    // 发送AI请求
                    string content = await ai.SendMessageBatchStringAsync(item.request);

                    // 计算耗时并记录
                    //var elapsed = DateTime.Now - requestStartTime;
                    //LogRequestTime(item.index, elapsed);

                    // 设置结果
                    completionSources[item.index].SetResult(content);
                }
                catch (Exception ex)
                {
                    // 对于异常，直接设置异常
                    completionSources[item.index].SetException(ex);
                }
            }
        }

        /// <summary>
        /// 记录请求耗时
        /// </summary>
        /// <param name="requestIndex">请求索引</param>
        /// <param name="elapsed">耗时</param>
        private void LogRequestTime(int requestIndex, TimeSpan elapsed)
        {
            ShowWaitForm.SetWaitFormShowError(
                $"请求 {requestIndex + 1} 耗时: {elapsed.TotalSeconds:F2} 秒 " +
                $"({elapsed.Hours:D2}:{elapsed.Minutes:D2}:{elapsed.Seconds:D2})");
        }

        /// <summary>
        /// 获取明细数据提取的提示词
        /// </summary>
        /// <returns>提示词文本</returns>
        private string GetDetailPrompt()
        {
            return @"
请精确提取以下明细数据，特别注意数字的准确识别：
1. 请仔细检查所有数值字段，特别是申报数量、单价、总价等数字
2. 注意区分相似的数字，如20000和2000，确保不要漏读数字位数
3. 对于每个数值，请逐位检查，确保完全准确提取
4. 不进行任何四舍五入或数值近似处理
5. 注意常识：数值只能有一个小数点的，避免识别错误
6. 提取字段：商品序号、备案序号、商品料号、商品编码、商品名称、申报计量单位、申报数量、单价、总价、币制、法定计量单位、法定数量、第二法定计量单位、第二法定数量、原产国；
7. 返回json格式
请以JSON数组格式返回，并确保数值字段的完整性和准确性：
举个例子【输入】

                 E6K
                 10C                        E6K10A
                  -     853890                 -          2351  0.23  4237  欧 千  10.7                                            全
      2     1                     外导体              个                                                    德国  中国
                 1CA     0000               1CA/90         23     41      .6    元 克 8                                           免
                 /90                           Z
                  Z
举个例子【输出】
[{
    ""商品序号"": 2,
    ""备案序号"": 1,
    ""商品料号"": ""E6K10C-1CA/90Z"",
    ""商品编码"": ""8538900000"",
    ""商品名称"": ""外导体"",
    ""申报计量单位"": ""个"",
    ""申报数量"": 235123,
    ""单价"": 0.2341,
    ""总价"": 4237.6,
    ""币制"": ""欧元"",
    ""法定计量单位"": ""千克"",
    ""法定数量"": 10.78,
    ""第二法定计量单位"": """",
    ""第二法定数量"":,
    ""原产国"": ""德国""
}]";
        }


        // 处理单个明细项
        private void ProcessDetailItem(dynamic item)
        {
            DataRow row = detailDT.NewRow();
            // 字符串字段处理（自动Trim，null/异常时返回空字符串）
            string GetStr(string field)
            {
                try { return (item[field]?.ToString() ?? "").Trim(); }
                catch { return ""; }
            }

            // 数值字段处理（null/异常时返回DBNull.Value）
            object GetNum(object field)
            {
                try
                {
                    var value = item[field];
                    if (value == null) return DBNull.Value;

                    // 尝试转换为数值
                    if (decimal.TryParse(value.ToString(), out decimal num))
                        return num;

                    return DBNull.Value;
                }
                catch
                {
                    return DBNull.Value;
                }
            }

            // 避免类型转换错误
            row["商品序号"] = GetStr("商品序号");
            row["备案序号"] = GetNum("备案序号");
            row["商品料号"] = GetStr("商品料号");
            row["商品编码"] = GetStr("商品编码");
            row["商品名称"] = GetStr("商品名称");
            row["申报计量单位"] = GetStr("申报计量单位");
            row["申报数量"] = GetNum("申报数量");
            row["剩余申报数量"] = GetNum("申报数量");
            row["已核销申报数量"] = 0;
            row["单价"] = GetNum("单价"); ;
            row["总价"] = GetNum("总价"); ;
            row["币制"] = GetStr("币制"); ;
            row["法定计量单位"] = GetStr("法定计量单位");
            row["法定数量"] = GetNum("法定数量");
            row["剩余法定数量"] = GetNum("法定数量");
            row["已核销法定数量"] = 0;
            row["第二法定计量单位"] = GetStr("第二法定计量单位");
            row["第二法定数量"] = GetNum("第二法定数量");
            row["剩余第二法定数量"] = GetNum("第二法定数量");
            row["已核销第二法定数量"] = 0;
            row["原产国"] = GetStr("原产国");
            detailDT.Rows.Add(row);
        }



        #region JSON数据处理

        /// <summary>
        /// 处理主表JSON数据并绑定到控件
        /// </summary>
        /// <param name="jsonText">AI返回的JSON文本</param>
        /// <param name="isFirstPage">是否为第一页数据</param>
        private void ProcessAndBindJsonData(string jsonText, bool isFirstPage = true)
        {
            try
            {
                // 提取JSON部分
                string jsonPart = ExtractJsonObject(jsonText);

                // 解析JSON数据
                dynamic jsonData = JsonConvert.DeserializeObject(jsonPart);

                if (jsonData == null)
                {
                    throw new Exception("JSON数据解析失败");
                }

                // 绑定数据到界面控件
                if (isFirstPage)
                {
                    BindMainTableData(jsonData);
                }

                // 更新数据源和刷新网格
                gd.DataSource = detailDT;
                gdv.RefreshData();
            }
            catch (Exception ex)
            {
                throw new Exception($"处理主表数据时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从文本中提取JSON对象
        /// </summary>
        /// <param name="text">包含JSON的文本</param>
        /// <returns>提取的JSON字符串</returns>
        private string ExtractJsonObject(string text)
        {
            int startIndex = text.IndexOf('{');
            int endIndex = text.LastIndexOf('}');
            if (startIndex < 0 || endIndex <= startIndex)
            {
                throw new Exception("未找到有效的JSON数据");
            }

            return text.Substring(startIndex, endIndex - startIndex + 1);
        }
        private string ExtractJsonArray(string text)
        {
            int startBrace = text.IndexOf('{');
            int startBracket = text.IndexOf('[');

            // 确定起始符号（优先处理数组格式）
            int startIndex = startBracket >= 0 ? startBracket : startBrace;
            if (startIndex < 0) throw new Exception("未找到有效的 JSON 数据");

            // 自动包装单个对象为数组
            if (text[startIndex] == '{')
            {
                return $"[{text.Substring(startIndex, text.LastIndexOf('}') - startIndex + 1)}]";
            }

            // 直接返回数组
            return text.Substring(startIndex, text.LastIndexOf(']') - startIndex + 1);
        }

        /// <summary>
        /// 绑定主表数据到界面控件
        /// </summary>
        /// <param name="jsonData">解析后的JSON数据</param>
        private void BindMainTableData(dynamic jsonData)
        {
            // 设置主要信息到文本框
            if (jsonData.核注清单编号 != null)
                txtHZQDBH.Text = jsonData.核注清单编号.ToString();

            if (jsonData.清单类型 != null)
                txtQDLX.Text = jsonData.清单类型.ToString();

            if (jsonData.报关单编号 != null)
                txtBGDBH.Text = jsonData.报关单编号.ToString();

            if (jsonData.正式核扣时间 != null)
            {
                if (DateTime.TryParse(jsonData.正式核扣时间.ToString(), out DateTime parsedDate))
                {
                    txtZSHKSJ.EditValue = parsedDate;
                }
            }

            if (jsonData.关联报关单编号 != null)
                txtGLBGDBH.Text = jsonData.关联报关单编号.ToString();

            // 设置操作人和操作时间
            txtCZR.Text = ICF.ISO.UNAME;
            txtCZSJ.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        /// <summary>
        /// 处理明细JSON数据并绑定到GridView
        /// </summary>
        /// <param name="jsonText">AI返回的JSON文本</param>
        private void ProcessAndBindDetailData(string jsonText)
        {
            try
            {
                // 尝试从文本中提取JSON数组部分
                string jsonPart = ExtractJsonArray(jsonText);

                // 解析JSON数据为明细列表
                var detailList = JsonConvert.DeserializeObject<List<dynamic>>(jsonPart);

                if (detailList == null || detailList.Count == 0)
                {
                    throw new Exception("未找到有效的明细数据");
                }

                // 处理每个明细项
                foreach (var item in detailList)
                {
                    ProcessDetailItem(item);
                }

                // 自动调整列宽
                gdv.BestFitColumns();
            }
            catch (Exception ex)
            {
                throw new Exception($"处理明细数据时出错: {ex.Message}");
            }
        }

        #endregion


        #region PDF内容提取

        /// <summary>
        /// 使用Spire.PDF提取指定页的PDF文件内容
        /// </summary>
        /// <param name="filePath">PDF文件路径</param>
        /// <param name="pageIndex">页码索引（从0开始）</param>
        /// <returns>提取的文本内容</returns>
        private StringBuilder ExtractPdfPageContent(string filePath, int pageIndex)
        {
            StringBuilder sb = new StringBuilder();

            try
            {
                // 创建PdfDocument对象
                using (PdfDocument pdf = new PdfDocument())
                {
                    // 加载PDF文件
                    pdf.LoadFromFile(filePath);

                    // 确保页码索引有效
                    if (pageIndex < 0 || pageIndex >= pdf.Pages.Count)
                    {
                        return sb; // 返回空内容
                    }

                    // 获取指定页面
                    PdfPageBase page = pdf.Pages[pageIndex];

                    // 提取页面文本
                    string text = page.ExtractText();

                    // 检查是否包含结束标记
                    if (ContainsEndMarker(text, out string processedText))
                    {
                        // 设置当前页为最后一页，这样就不会继续处理后续页面
                        currentPageIndex = totalPages - 1;
                        text = processedText;
                    }

                    sb.AppendLine(text);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断流程
                ShowWaitForm.SetWaitFormShowError($"提取PDF第{pageIndex + 1}页时出错: {ex.Message}");
            }

            return sb;
        }

        /// <summary>
        /// 检查文本是否包含结束标记
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <param name="processedText">处理后的文本</param>
        /// <returns>是否包含结束标记</returns>
        private bool ContainsEndMarker(string text, out string processedText)
        {
            processedText = text;

            // 检查是否包含"简单加工成品"标记
            int index = text.IndexOf("简单加工成品");
            if (index != -1)
            {
                // 只保留标记之前的内容
                processedText = text.Substring(0, index);
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取PDF文件的总页数
        /// </summary>
        /// <param name="filePath">PDF文件路径</param>
        /// <returns>PDF总页数</returns>
        private int GetPdfPageCount(string filePath)
        {
            try
            {
                // 创建PdfDocument对象
                using (PdfDocument pdf = new PdfDocument())
                {
                    // 加载PDF文件
                    pdf.LoadFromFile(filePath);
                    // 返回总页数
                    return pdf.Pages.Count;
                }
            }
            catch (Exception ex)
            {
                ShowWaitForm.SetWaitFormShowError($"获取PDF页数时出错: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #endregion



        #region 计时器管理

        /// <summary>
        /// 计时器Tick事件处理
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            if (isTimerRunning)
            {
                // 计算已经过去的时间
                elapsedTime = DateTime.Now - startTime;

                // 更新时间显示，格式为 HH:mm:ss
                UpdateTimerDisplay();
            }
        }

        /// <summary>
        /// 更新计时器显示
        /// </summary>
        private void UpdateTimerDisplay()
        {
            txtTime.Text = elapsedTime.ToString(@"hh\:mm\:ss");
        }

        /// <summary>
        /// 开始计时
        /// </summary>
        private void StartTimer()
        {
            // 重置计时器状态
            startTime = DateTime.Now;
            elapsedTime = TimeSpan.Zero;
            isTimerRunning = true;

            // 启动计时器
            timer.Start();

            // 初始显示
            txtTime.Text = "00:00:00";
        }

        /// <summary>
        /// 停止计时
        /// </summary>
        private void StopTimer()
        {
            if (isTimerRunning)
            {
                // 停止计时器
                timer.Stop();
                isTimerRunning = false;

                // 计算最终时间
                elapsedTime = DateTime.Now - startTime;
                UpdateTimerDisplay();
            }
        }

        #endregion

        private void ckbEdit_CheckedChanged(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gdv.OptionsBehavior.Editable = ckbEdit.Checked;
        }
    }
}
